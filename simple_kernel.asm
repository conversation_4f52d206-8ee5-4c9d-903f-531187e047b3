BITS 16
ORG 0x0000

; ============================================================
;  Simple Working Kernel
; ============================================================
start:
    ;---------------------------------------------------------
    ; Segment / stack setup - do this FIRST
    ;---------------------------------------------------------
    mov ax, 0x8000          ; Where Stage-2 dropped the kernel
    mov ds, ax              ; Data
    mov es, ax              ; Extra (misc)
    mov ss, ax              ; Stack
    mov sp, 0xFFFE          ;   ...top of segment

    cli                     ; Safe while we poke at flags
    cld                     ; Forward string ops
    sti

    ;---------------------------------------------------------
    ; Immediate visual feedback that kernel started
    ;---------------------------------------------------------
    mov ah, 0x0E
    mov al, 'K'             ; K for Kernel
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'R'
    int 0x10
    mov al, 'N'
    int 0x10

    ;---------------------------------------------------------
    ; Clear the screen and greet the user
    ;---------------------------------------------------------
    call clear_screen

    ; Print startup message manually first to test
    mov ah, 0x0E
    mov al, 'O'
    int 0x10
    mov al, 'K'
    int 0x10
    mov al, '!'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Now try to print strings using CS segment
    mov si, startup_msg
    call print_string_cs

    mov si, banner
    call print_string_cs

; ============================================================
;  Main command loop
; ============================================================
main_loop:
    mov si, prompt
    call print_string

    mov di, command_buffer
    call read_command

    ; Check for "help" command
    mov si, command_buffer
    mov di, cmd_help
    call compare_strings
    cmp al, 1
    je .show_help

    ; Check for "test" command
    mov si, command_buffer
    mov di, cmd_test
    call compare_strings
    cmp al, 1
    je .show_test

    ; Check for "reboot" command
    mov si, command_buffer
    mov di, cmd_reboot
    call compare_strings
    cmp al, 1
    je .reboot

    ; Check for empty command
    mov al, [command_buffer]
    cmp al, 0
    je main_loop

    ; Unknown command
    mov si, unknown_cmd_msg
    call print_string
    jmp main_loop

.show_help:
    mov si, help_message
    call print_string
    jmp main_loop

.show_test:
    mov si, test_message
    call print_string
    jmp main_loop

.reboot:
    mov si, reboot_msg
    call print_string
    mov cx, 0xFFFF
.delay:
    nop
    loop .delay
    mov al, 0xFE
    out 0x64, al
    int 3
    jmp $

; ============================================================
;  Utility Functions
; ============================================================

clear_screen:
    push ax
    mov ah, 0x00
    mov al, 0x03
    int 0x10
    pop ax
    ret

; Fixed print_string function
print_string:
    push ax
    push si
.loop:
    lodsb                   ; Load byte from DS:SI into AL, increment SI
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop si
    pop ax
    ret

; Print string using CS segment (where the code and data are)
print_string_cs:
    push ax
    push si
    push ds

    ; Use CS as data segment since that's where our strings are
    mov ax, cs
    mov ds, ax

.loop:
    lodsb                   ; Load byte from DS:SI into AL, increment SI
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop ds
    pop si
    pop ax
    ret

print_hex_word:
    push ax
    push bx
    push cx
    mov bx, ax
    mov cl, 4
    mov al, bh
    shr al, cl
    call print_hex_digit
    mov al, bh
    and al, 0x0F
    call print_hex_digit
    mov al, bl
    shr al, cl
    call print_hex_digit
    mov al, bl
    and al, 0x0F
    call print_hex_digit
    pop cx
    pop bx
    pop ax
    ret

print_hex_digit:
    push ax
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    pop ax
    ret

read_command:
    push ax
    push bx
    push cx
    mov cx, 0
.read_loop:
    mov ah, 0x00
    int 0x16
    cmp al, 0x0D
    je .done
    cmp al, 0x08
    je .handle_backspace
    cmp al, 32
    jb .read_loop
    cmp al, 126
    ja .read_loop
    cmp cx, 62
    jae .read_loop
    mov [di], al
    inc di
    inc cx
    mov ah, 0x0E
    int 0x10
    jmp .read_loop
.handle_backspace:
    cmp cx, 0
    je .read_loop
    dec di
    dec cx
    mov ah, 0x0E
    mov al, 0x08
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 0x08
    int 0x10
    jmp .read_loop
.done:
    mov byte [di], 0
    mov ah, 0x0E
    mov al, 0x0D
    int 0x10
    mov al, 0x0A
    int 0x10
    pop cx
    pop bx
    pop ax
    ret

compare_strings:
    push si
    push di
    push bx
.cmp_loop:
    mov al, [si]
    mov bl, [di]
    cmp al, 'A'
    jb .no_conv1
    cmp al, 'Z'
    ja .no_conv1
    add al, 32
.no_conv1:
    cmp bl, 'A'
    jb .no_conv2
    cmp bl, 'Z'
    ja .no_conv2
    add bl, 32
.no_conv2:
    cmp al, bl
    jne .not_equal
    test al, al
    je .equal
    inc si
    inc di
    jmp .cmp_loop
.not_equal:
    mov al, 0
    jmp .exit
.equal:
    mov al, 1
.exit:
    pop bx
    pop di
    pop si
    ret

; ============================================================
;  Data section
; ============================================================

startup_msg     db 'Kernel loaded successfully!', 13, 10, 0
banner          db 13,10, '==============================', 13,10
                db      '    SIMPLE KERNEL v1.0', 13,10
                db      '==============================', 13,10
                db      'Commands: help, test, reboot', 13,10, 13,10, 0

prompt          db '> ', 0
help_message    db 'Available commands:', 13, 10
                db '  help   - Show this help', 13, 10
                db '  test   - Run kernel test', 13, 10
                db '  reboot - Restart system', 13, 10, 0

test_message    db 'Kernel test passed!', 13,10
                db 'All systems functional.', 13,10, 0

unknown_cmd_msg db 'Unknown command. Type "help" for available commands.', 13, 10, 0
reboot_msg      db 'Rebooting system...', 13, 10, 0

cmd_help        db 'help', 0
cmd_reboot      db 'reboot', 0
cmd_test        db 'test', 0

command_buffer  times 64 db 0

; Pad to fill exactly three 512-byte sectors (1536 bytes)
times 1536-($-$$) db 0